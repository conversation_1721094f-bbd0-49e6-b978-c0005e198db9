server:
  name: dingtalk-robot-send-message
  description: 钉钉机器人发消息和DING消息
  default_active: true
  securitySchemes:
    - id: DingTalkAuth
      type: apiKey
      in: header
      name: x-acs-dingtalk-access-token
tools:
  - name: sendDINGMessageByRobot
    description: 机器人发送DING消息
    args:
      - name: robotCode
        description: 机器人Code
        type: string
        position: body
        system: ROBOT_CODE
      - name: remindType
        type: string
        description: DING消息类型。1：应用内DING，2：短信DING，3：电话DING；默认值为1。
        position: body
        required: true
      - name: receiverUserIdList
        description: 接收人userId列表。应用内DING消息，每次接收人不能超过200个。短信DING和电话DING，每次接收人不能超过20个。
        type: array
        required: true
        position: body
        items:
          type: string
      - name: content
        description: DING消息内容。
        type: string
        required: true
        position: body
    requestTemplate:
      url: https://api.dingtalk.com//v1.0/robot/ding/send
      method: POST
      headers:
        - key: Content-Type
          value: application/json
      security:
        id: DingTalkAuth
    responseTemplate: {}

  - name: recallDINGMessage
    description: 撤回机器人发送DING的消息
    args:
      - name: robotCode
        description: 机器人Code
        type: string
        position: body
        system: ROBOT_CODE
      - name: openDingId
        type: string
        description: 需要被撤回的DING消息ID。
        position: body
        required: true
    requestTemplate:
      url: https://api.dingtalk.com//v1.0/robot/ding/recall
      method: POST
      headers:
        - key: Content-Type
          value: application/json
      security:
        id: DingTalkAuth
    responseTemplate: { }

  - name: sendGroupMessageByRobot
    description: 机器人发送群消息
    args:
      - name: robotCode
        description: 机器人Code
        type: string
        position: body
        system: ROBOT_CODE
      - name: openConversationId
        type: string
        description: 会话ID。
        position: body
        required: true
      - name: msgKey
        type: string
        description: 消息模板key，默认为"sampleMarkdown"。
        position: body
        default: sampleMarkdown
        # 不需要模型处理
        not_need_model_transform: true
      - name: msgParam
        type: object
        extendType: json
        description: sampleMarkdown消息内容
        position: body
        required: true
        items:
          type: object
          properties:
            title:
              type: string
              description: 消息标题。
            text:
              type: string
              description: 消息内容，markdown格式。



    requestTemplate:
      url: https://api.dingtalk.com/v1.0/robot/groupMessages/send
      method: POST
      headers:
        - key: Content-Type
          value: application/json
      security:
        id: DingTalkAuth
    responseTemplate: { }

  - name: recallGroupMessageByRobot
    description: 撤回机器人发送的群消息
    args:
      - name: robotCode
        description: 机器人Code
        type: string
        position: body
        system: ROBOT_CODE
      - name: openConversationId
        type: string
        description: 会话ID。
        position: body
        required: true
      - name: processQueryKeys
        type: array
        description: 要撤回的消息ID列表。
        position: body
        required: true
        items:
          type: string

    requestTemplate:
      url: https://api.dingtalk.com/v1.0/robot/groupMessages/recall
      method: POST
      headers:
        - key: Content-Type
          value: application/json
      security:
        id: DingTalkAuth
    responseTemplate: { }

  - name: batchSendUserMessageByRobot
    description: 机器人批量给人发送消息
    args:
      - name: robotCode
        description: 机器人Code
        type: string
        position: body
        system: ROBOT_CODE
      - name: userIds
        type: array
        description: 用户userId列表。
        position: body
        required: true
        items:
          type: string
      - name: msgKey
        type: string
        description: 消息模板key，默认为"sampleMarkdown"。
        position: body
        default: sampleMarkdown
        # 不需要模型处理
        not_need_model_transform: true
      - name: msgParam
        type: object
        # 扩展类型，最终发送给API时，转换为json string
        extendType: json
        description: sampleMarkdown消息内容
        position: body
        required: true
        items:
          type: object
          properties:
            title:
              type: string
              description: 消息标题。
            text:
              type: string
              description: 消息内容，markdown格式。

    requestTemplate:
      url: https://api.dingtalk.com/v1.0/robot/oToMessages/batchSend
      method: POST
      headers:
        - key: Content-Type
          value: application/json
      security:
        id: DingTalkAuth
    responseTemplate: { }

  - name: batchRecallUserMessageByRobot
    description: 批量撤回机器人给人发送的消息
    args:
      - name: robotCode
        description: 机器人Code
        type: string
        position: body
        system: ROBOT_CODE
      - name: processQueryKeys
        type: array
        description: 要撤回的消息ID列表。
        position: body
        required: true
        items:
          type: string

    requestTemplate:
      url: https://api.dingtalk.com/v1.0/robot/oToMessages/batchRecall
      method: POST
      headers:
        - key: Content-Type
          value: application/json
      security:
        id: DingTalkAuth
    responseTemplate: { }
  - name: sendMessageByCustomRobot
    description: 群自定义机器人发送群消息
    args:
      - name: msgtype
        description: 消息类型，默认为markdown
        type: string
        position: body
        default: markdown
        # 不需要模型处理
        not_need_model_transform: true
      - name: markdown
        type: object
        position: body
        description: markdown消息
        items:
          type: object
          properties:
            title:
              type: string
              description: 消息标题。
            text:
              type: string
              description: 消息内容，markdown格式。
      - name: at
        description: AT群成员
        position: body
        type: object
        items:
          type: object
          properties:
            isAtAll:
              type: boolean
              description: 是否AT所有人，默认为false。
            atUserIds:
              type: array
              items:
                type: string
              description: 被AT的群成员userId。

    requestTemplate:
      url: https://oapi.dingtalk.com/robot/send?access_token=ROBOT_ACCESS_TOKEN
      method: POST
      headers:
        - key: Content-Type
          value: application/json
    responseTemplate: { }