server:
  name: dingtalk-teambition
  description: 钉钉Teambition-项目管理
tools:

  - name: createProject
    description: 创建项目
    requestTemplate:
      method: POST
      url: https://api.dingtalk.com/v1.0/project/users/{userId}/projects
    args:
      - name: userId
        description: 操作者userId。
        type: string
        required: true
        position: path
      - name: name
        description: 项目名称。
        type: string
        required: true
        position: body


  - name: queryProjectsByProjectName
    description: 根据项目名称模糊查询项目信息
    requestTemplate:
      method: POST
      url: https://api.dingtalk.com/v1.0/project/users/{userId}/projects/query
    args:
      - name: userId
        description: 操作者userId。
        type: string
        required: true
        position: path
      - name: name
        description: 项目名称，模糊查询指定项目名称的项目信息
        type: string
        required: true
        position: body
      - name: maxResults
        description: 分页大小，默认10，最大300
        type: number
        required: false
        position: body
      - name: nextToken
        description: 分页标。供分页使用，下一页token，从当前查询结果中获取。
        type: string
        required: false
        position: body

  - name: queryProjectStatusesByProjectId
    description: 根据项目ID查询项目状态信息
    requestTemplate:
      method: GET
      url: https://api.dingtalk.com/v1.0/project/users/{userId}/projects/{projectId}/statuses
    args:
      - name: userId
        description: 操作者userId。
        type: string
        required: true
        position: path
      - name: projectId
        description: 项目ID。
        type: string
        required: true
        position: path

  - name: queryProjectMembersByProjectId
    description: 根据项目ID查询项目成员信息
    requestTemplate:
      method: GET
      url: https://api.dingtalk.com/v1.0/project/users/{userId}/projects/{projectId}/members?maxResults={String}
    args:
      - name: userId
        description: 操作者userId。
        type: string
        required: true
        position: path
      - name: projectId
        description: 项目ID。
        type: string
        required: true
        position: path
      - name: maxResults
        description: 分页大小，默认10，最大300
        type: number
        required: false
        position: query

  - name: addProjectMembers
    description: 添加项目成员
    requestTemplate:
      method: POST
      url: https://api.dingtalk.com/v1.0/project/users/{userId}/projects/{projectId}/members
    args:
      - name: userId
        description: 操作者userId。
        type: string
        required: true
        position: path
      - name: projectId
        description: 项目ID。
        type: string
        required: true
        position: path
      - name: userIds
        description: 被添加的用户userId列表，建议一次不超过10个。
        type: array
        required: true
        position: body
        items:
          type: string

  - name: removeProjectMembers
    description: 添加项目成员
    requestTemplate:
      method: POST
      url: https://api.dingtalk.com/v1.0/project/users/{userId}/projects/{projectId}/members/remove
    args:
      - name: userId
        description: 操作者userId。
        type: string
        required: true
        position: path
      - name: projectId
        description: 项目ID。
        type: string
        required: true
        position: path
      - name: userIds
        description: 被删除的用户userId列表，建议一次不超过10个。
        type: array
        required: true
        position: body
        items:
          type: string


  # 项目任务
  - name: listProjectTasks
    description: 查询项目中的任务列表
    requestTemplate:
      method: GET
      url: https://api.dingtalk.com/v1.0/project/users/{userId}/projectIds/{projectId}/tasks?nextToken=String&maxResults=Integer
    args:
      - name: userId
        description: 操作者userId。
        type: string
        required: true
        position: path
      - name: projectId
        description: 项目ID。
        type: string
        required: true
        position: path
      - name: nextToken
        description: 分页游标。如果是首次调用，该参数不传。如果是非首次调用，该参数传上次接口返回的nextToken。
        type: string
        required: true
        position: query
      - name: maxResults
        description: 每页返回最大数量。默认10，最大500。
        type: number
        required: false
        position: query

  - name: listUserProjectTasks
    description: 查询用户项目任务信息列表
    requestTemplate:
      method: POST
      url: https://api.dingtalk.com/v1.0/project/users/{userId}/tasks/search?roleTypes=String&tql=String&nextToken=String&maxResults=String
    args:
      - name: userId
        description: 操作者userId。
        type: string
        required: true
        position: path
      - name: roleTypes
        description: 用户的任务角色。creator、executor、involveMember 中的一个或多个，多个以英文逗号拼接。例如：creator,executor
        type: string
        required: true
        position: query
      - name: nextToken
        description: 分页游标。如果是首次调用，该参数不传。如果是非首次调用，该参数传上次接口返回的nextToken。
        type: string
        required: true
        position: query
      - name: maxResults
        description: 每页返回最大数量。默认10，最大500。
        type: number
        required: false
        position: query

  - name: queryProjectTaskDetail
    description: 查询项目任务详情
    requestTemplate:
      method: GET
      url: https://api.dingtalk.com/v1.0/project/users/{userId}/tasks?taskId=String
    args:
      - name: userId
        description: 操作者userId。
        type: string
        required: true
        position: path
      - name: taskId
        description: 项目任务ID
        type: string
        required: true
        position: query

  - name: createProjectTask
    description: 创建项目任务
    requestTemplate:
      method: POST
      url: https://api.dingtalk.com/v1.0/project/users/{userId}/tasks
    args:
      - name: userId
        description: 操作者userId。
        type: string
        required: true
        position: path
      - name: projectId
        description: 项目id。
        type: string
        required: true
        position: body
      - name: content
        description: 任务标题
        type: string
        required: true
        position: body
      - name: note
        description: 任务备注
        type: string
        required: false
        position: body
      - name: startDate
        description: 任务截止时间，格式：YYYY-MM-DDTHH:mm:ssZ（ISO 8601/RFC 3339）。
        type: string
        required: false
        position: body
      - name: dueDate
        description: 任务开始时间，格式：YYYY-MM-DDTHH:mm:ssZ（ISO 8601/RFC 3339）。
        type: string
        required: false
        position: body
      - name: executorId
        description: 任务执行者userId。
        type: string
        required: false
        position: body

  - name: deleteProjectTask
    description: 删除项目任务
    requestTemplate:
      method: DELETE
      url: https://api.dingtalk.com/v1.0/project/users/{userId}/tasks/{taskId}
    args:
      - name: userId
        description: 操作者userId。
        type: string
        required: true
        position: path
      - name: taskId
        description: 任务ID
        type: string
        required: true
        position: path


  - name: updateProjectTaskNote
    description: 更新项目任务备注
    requestTemplate:
      method: PUT
      url: https://api.dingtalk.com/v1.0/project/users/{userId}/tasks/{taskId}/notes
    args:
      - name: userId
        description: 操作者userId。
        type: string
        required: true
        position: path
      - name: taskId
        description: 任务ID。
        type: string
        required: true
        position: path
      - name: note
        description: 任务备注
        type: string
        required: true
        position: body

  - name: updateProjectTaskContent
    description: 更新项目任务标题
    requestTemplate:
      method: PUT
      url: https://api.dingtalk.com/v1.0/project/users/{userId}/tasks/{taskId}/contents
    args:
      - name: userId
        description: 操作者userId。
        type: string
        required: true
        position: path
      - name: taskId
        description: 任务ID。
        type: string
        required: true
        position: path
      - name: content
        description: 任务标题
        type: string
        required: true
        position: body

  - name: updateProjectTaskExecutors
    description: 更新项目任务执行者
    requestTemplate:
      method: PUT
      url: https://api.dingtalk.com/v1.0/project/users/{userId}/tasks/{taskId}/executors
    args:
      - name: userId
        description: 操作者userId。
        type: string
        required: true
        position: path
      - name: taskId
        description: 任务ID。
        type: string
        required: true
        position: path
      - name: executorId
        description: 任务执行者userId
        type: string
        required: true
        position: body


  - name: updateProjectTaskInvolveMembers
    description: 更新项目任务参与者
    requestTemplate:
      method: PUT
      url: https://api.dingtalk.com/v1.0/project/users/{userId}/tasks/{taskId}/involveMembers
    args:
      - name: userId
        description: 操作者userId。
        type: string
        required: true
        position: path
      - name: taskId
        description: 任务ID。
        type: string
        required: true
        position: path
      - name: involveMembers
        description: 任务参与者userId列表
        type: array
        required: true
        position: body
        items:
          type: string
