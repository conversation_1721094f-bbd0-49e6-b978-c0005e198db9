server:
  name: dingtalk-honor
  version: 1.0.0
  description: DingTalk Honor Management MCP Server - 钉钉企业文化荣誉管理服务

tools:
  # 1. 给员工颁发荣誉
  - name: mcp_dingtalk-honor_grantHonor
    description: |
      给组织内的员工颁发荣誉，支持单人或多人批量颁发。
      颁发后员工会收到荣誉推送消息，可以选择佩戴荣誉挂件。
      注意：同一个颁发人不允许并发执行，需要串行调用。
    requestTemplate:
      method: POST
      url: "https://api.dingtalk.com/v1.0/orgCulture/honors/{honorId}/grant"
    args:
      - name: honorId
        type: string
        description: "荣誉ID，可通过查询企业荣誉列表接口获取"
        position: path
        required: true
      - name: senderUserId
        type: string
        description: "发送人userId。注意：同一个发送人给多个员工颁发荣誉时，请分别按顺序执行，不允许并发执行"
        position: body
        required: true
      - name: grantReason
        type: string
        description: "颁奖词，最多50个字符"
        position: body
        required: true
      - name: granterName
        type: string
        description: "颁奖人名字，最多15个字符"
        position: body
        required: true
      - name: receiverUserIds
        type: array
        description: "接收人userId列表，最多10个"
        position: body
        required: true
        items:
          type: string
      - name: expirationTime
        type: number
        description: "荣誉有效期到期时间戳（毫秒）。不传代表永久有效。有效期时间范围要求1~366天后"
        position: body
        required: false
      - name: noticeSingle
        type: boolean
        description: "是否发送单聊通知。true:发送，false:不发送"
        position: body
        required: false
      - name: noticeAnnouncer
        type: boolean
        description: "是否使用官宣号通知获奖人。true:通知，false:不通知"
        position: body
        required: false
      - name: openConversationIds
        type: array
        description: "接收荣誉消息的群openConversationId列表，最多5个。注意：发送人必须是群成员"
        position: body
        required: false
        items:
          type: string

  # 2. 查询当前企业下可颁发的荣誉列表
  - name: mcp_dingtalk-honor_queryOrgHonors
    description: |
      查询当前企业下可颁发的荣誉列表，包括荣誉ID、名称、描述、图标等信息。
      支持分页查询，可用于展示荣誉选择列表。
    requestTemplate:
      method: GET
      url: "https://api.dingtalk.com/v1.0/orgCulture/organizations/honors?nextToken=String&maxResults=Integer"
    args:
      - name: nextToken
        type: string
        description: "分页游标。首次调用传0，非首次调用传上次返回的nextToken值"
        position: query
        required: true
      - name: maxResults
        type: number
        description: "每页最大条目数，默认值20，最大值100"
        position: query
        required: false

  # 3. 查询员工已获得的组织荣誉
  - name: mcp_dingtalk-honor_queryUserHonors
    description: |
      查询某个员工获得的组织荣誉记录，包括荣誉ID、名称、授予时间、发放人等信息。
      可用于展示员工的荣誉墙或荣誉历史。
    requestTemplate:
      method: GET
      url: "https://api.dingtalk.com/v1.0/orgCulture/honors/users/{userId}?nextToken=String&maxResults=Integer"
    args:
      - name: userId
        type: string
        description: "要查询的员工userId"
        position: path
        required: true
      - name: nextToken
        type: string
        description: "分页游标。首次查询传0，非首次查询传上次返回的nextToken"
        position: query
        required: true
      - name: maxResults
        type: number
        description: "每页返回的最大条目数，默认20，最大100"
        position: query
        required: false

  # 4. 创建荣誉勋章模板
  - name: mcp_dingtalk-honor_createOrgHonor
    description: |
      创建企业荣誉勋章模板。创建后会流入钉钉后台审核，一般5个工作日内审核完毕。
      注意事项：
      1. 需要主管理员或子管理员权限
      2. 企业每周的审核次数有限制（标准版每周10次/专业版每周20次）
      3. 不允许并发调用，如需批量创建请串行执行
      4. 图片需要先调用上传媒体文件接口获取media_id
    requestTemplate:
      method: POST
      url: "https://api.dingtalk.com/v1.0/orgCulture/honors/templates"
    args:
      - name: userId
        type: string
        description: "创建荣誉勋章模板的管理员userId，需要主管理员或子管理员角色"
        position: body
        required: true
      - name: medalName
        type: string
        description: "荣誉勋章名称，最大长度10字符，不支持表情图标"
        position: body
        required: true
      - name: medalDesc
        type: string
        description: "荣誉勋章描述，最大长度30字符，不支持表情图标"
        position: body
        required: true
      - name: medalMediaId
        type: string
        description: "荣誉勋章图片的media_id（900x900，不超过1M，PNG格式）"
        position: body
        required: true
      - name: avatarFrameMediaId
        type: string
        description: "头像挂件图片的media_id（240x240，不超过1M，PNG格式）"
        position: body
        required: true
      - name: defaultBgColor
        type: string
        description: "背景颜色，可选值：#FFFBB4, #FFE7BC, #FFDAF4, #DAF6A8, #E4D7FF, #BFDFFF, #B9F2D6"
        position: body
        required: true

  # 5. 撤销员工获得的荣誉勋章
  - name: mcp_dingtalk-honor_recallHonor
    description: |
      撤销员工已获得的荣誉勋章。
      撤销后员工将无法继续佩戴该荣誉。
    requestTemplate:
      method: POST
      url: "https://api.dingtalk.com/v1.0/orgCulture/honors/{honorId}/recall"
    args:
      - name: honorId
        type: string
        description: "要撤销的荣誉勋章ID，可通过查询员工已获得的组织荣誉接口获取"
        position: path
        required: true
      - name: userId
        type: string
        description: "要撤销荣誉的员工userId"
        position: body
        required: true
