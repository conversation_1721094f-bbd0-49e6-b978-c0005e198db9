/**
 * ServiceWindow Message Builder - 核心特色功能
 * 简化复杂的服务窗消息格式构建
 */

interface ServiceWindowMessage {
    userId: string;
    messageTitle: string;
    messageContent: string;
    accountId: string;
}

interface BatchServiceWindowMessage {
    userIdList: string[];
    messageTitle: string;
    messageContent: string;
    accountId: string;
}

export class ServiceWindowMessageBuilder {
    /**
     * 构建服务窗消息体
     * @param message 用户友好的消息参数
     * @returns 钉钉API标准格式的消息体
     */
    static buildSendServiceWindowMarkdownBody(message: ServiceWindowMessage) {
        return {
            detail: {
                msgType: "markdown",
                uuid: this.generateMessageUuid(),
                userId: message.userId,
                messageBody: {
                    markdown: {
                        title: message.messageTitle,
                        text: message.messageContent
                    }
                }
            },
            accountId: message.accountId
        };
    }

    static buildBatchSendServiceWindowMarkdownBody(message: BatchServiceWindowMessage) {
        return {
            detail: {
                msgType: "markdown",
                uuid: this.generateMessageUuid(),
                userIdList: message.userIdList,
                messageBody: {
                    markdown: {
                        title: message.messageTitle,
                        text: message.messageContent
                    }
                }
            },
            accountId: message.accountId
        };
    }

    /**
     * 生成唯一的消息ID
     */
    static generateMessageUuid(): string {
        const UUID = crypto.randomUUID();
        return UUID.toString();
    }
}
