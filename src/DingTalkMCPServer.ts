import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import { CallToolRequestSchema, ListToolsRequestSchema } from '@modelcontextprotocol/sdk/types.js';
import { ServiceWindowMessageBuilder } from './utils/messageBuilder.js';
import { LogMessageBuilder } from './utils/logBuilder.js';
import axios from 'axios';
import * as yaml from 'js-yaml';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { 
    MCPTool, 
    TokenCacheData, 
    DingTalkTokenResponse, 
    ToolExecutionResult 
} from './types.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

export class DingTalkMCPServer {
    private server: Server;
    private accessToken: string | null = null;
    private readonly appId: string;
    private readonly appSecret: string;
    private readonly tokenCacheFile: string;
    private tokenCacheData: TokenCacheData | null = null;
    private tools: MCPTool[] = [];
    private debug: string | undefined;

    constructor() {
        this.accessToken = process.env.DINGTALK_ACCESS_TOKEN || null;
        this.appId = process.env.DINGTALK_Client_ID!;
        this.appSecret = process.env.DINGTALK_Client_Secret!;
        this.debug = process.env.DEBUG;
        
        // Token缓存配置
        this.tokenCacheFile = path.join(__dirname, '..', '.dingtalk_token_cache.json');
        
        this.loadConfig(path.join(__dirname, '..'));
        
        // 测试状态
        if (process.env.STAGING) {
            this.loadConfig(path.join(__dirname, '../staging'));
        }

        this.server = new Server({
            name: 'Dingtalk MCP Server',
            description: "Dingtalk MCP Server, inclued tasks, contacts, calendar, robot etc.",
            version: '1.0.0',
            capabilities: {
                tools: {},
            },
        });

        this.loadTokenCache();
        this.setupHandlers();
    }

    private loadConfig(dirname: string): void {
        try {
            // 激活的profile
            const profiles = process.env.ACTIVE_PROFILES;
            let profiles_list: string[] = [];
            if (profiles) {
                profiles_list = profiles.split(',');
            }

            const configDir = path.join(dirname);
            fs.readdirSync(configDir).forEach((file) => {
                if (file.endsWith('mcp_server.yaml')) {
                    console.error("found mcp config file:", file);

                    const configContent = fs.readFileSync(path.join(configDir, file), 'utf8');
                    const config = yaml.load(configContent) as any;
                    if (!config) {
                        throw new Error('Config is empty');
                    }
                    
                    if (profiles_list.includes("ALL") || 
                        profiles_list.includes(config.server.name) || 
                        (profiles_list.length == 0 && config.server.default_active)) {
                        
                        (config.tools || []).forEach((tool: MCPTool) => {
                            if (this.tools.find(t => t.name === tool.name)) {
                                throw new Error('Duplicate tool name: ' + tool.name);
                            }
                            this.tools.push(tool);
                        });
                    }
                }
            });

            if (this.tools && this.tools.length > 0) {
                console.error(`Loaded ${this.tools.length} tools from config`);
                console.error(`Tools:\r\n${this.tools.map(t => 
                    t.name + ', ' + t.requestTemplate.method + ' ' + t.requestTemplate.url + ', ' + t.description
                ).join('\r\n')}`);
            }
        } catch (error) {
            const err = error as Error;
            console.error('Failed to load config:', err.message);
            this.tools = [];
            throw error;
        }
    }

    /**
     * 加载本地缓存的access_token
     */
    private loadTokenCache(): void {
        try {
            if (fs.existsSync(this.tokenCacheFile)) {
                const cacheContent = fs.readFileSync(this.tokenCacheFile, 'utf8');
                const cacheData = JSON.parse(cacheContent) as TokenCacheData;
                
                // 检查缓存是否有效
                if (this.isTokenCacheValid(cacheData)) {
                    this.tokenCacheData = cacheData;
                    this.accessToken = cacheData.access_token;
                    console.error('Loaded valid access token from cache');
                    console.error(`Token expires at: ${new Date(cacheData.expires_at).toISOString()}`);
                } else {
                    console.error('Cached token expired, will refresh when needed');
                    this.clearTokenCache();
                }
            } else {
                console.error('No token cache found');
            }
        } catch (error) {
            const err = error as Error;
            console.error('Failed to load token cache:', err.message);
            this.clearTokenCache();
        }
    }

    /**
     * 检查token缓存是否有效（未过期）
     */
    private isTokenCacheValid(cacheData: TokenCacheData): boolean {
        if (!cacheData || !cacheData.access_token || !cacheData.expires_at || cacheData.app_id !== this.appId) {
            return false;
        }
        
        // 提前5分钟刷新token，避免在请求过程中过期
        const bufferTime = 5 * 60 * 1000; // 5分钟
        const now = Date.now();
        return now < (cacheData.expires_at - bufferTime);
    }

    /**
     * 保存access_token到本地缓存
     */
    private saveTokenCache(accessToken: string, expiresIn: number = 7200): void {
        try {
            const now = Date.now();
            const expiresAt = now + (expiresIn * 1000); // 转换为毫秒
            
            this.tokenCacheData = {
                access_token: accessToken,
                expires_in: expiresIn,
                expires_at: expiresAt,
                created_at: now,
                app_id: this.appId
            };
            
            fs.writeFileSync(this.tokenCacheFile, JSON.stringify(this.tokenCacheData, null, 2));
            console.error('Access token saved to cache');
            console.error(`Token expires at: ${new Date(expiresAt).toISOString()}`);
        } catch (error) {
            const err = error as Error;
            console.error('Failed to save token cache:', err.message);
        }
    }

    /**
     * 清除token缓存
     */
    private clearTokenCache(): void {
        try {
            if (fs.existsSync(this.tokenCacheFile)) {
                fs.unlinkSync(this.tokenCacheFile);
            }
            this.tokenCacheData = null;
            this.accessToken = null;
        } catch (error) {
            const err = error as Error;
            console.error('Failed to clear token cache:', err.message);
        }
    }

    /**
     * 获取有效的access_token（优先使用缓存）
     */
    private async getValidAccessToken(): Promise<string> {
        // 如果有缓存的有效token，直接返回
        if (this.tokenCacheData && this.isTokenCacheValid(this.tokenCacheData)) {
            return this.tokenCacheData.access_token;
        }

        // 否则刷新token
        return await this.refreshAccessToken();
    }

    private async refreshAccessToken(): Promise<string> {
        try {
            console.error('Refreshing access token...');
            
            const response = await axios.post<DingTalkTokenResponse>(
                'https://oapi.dingtalk.com/gettoken',
                null,
                {
                    params: {
                        appkey: this.appId,
                        appsecret: this.appSecret
                    }
                }
            );

            if (response.data.errcode !== 0) {
                throw new Error(`Failed to get access token: ${response.data.errmsg}`);
            }

            const { access_token, expires_in } = response.data;
            this.accessToken = access_token;
            
            // 保存到缓存
            this.saveTokenCache(access_token, expires_in);
            
            console.error('Access token refreshed successfully');
            return access_token;
        } catch (error) {
            const err = error as Error;
            console.error('Failed to refresh access token:', err.message);
            throw error;
        }
    }

    private setupHandlers(): void {
        // 实现处理器设置
        // 这里需要更多的实现细节
    }

    private generateSchema(tool: MCPTool): any {
        // 实现模式生成
        // 这里需要更多的实现细节
    }

    private async executeTool(name: string, args: any): Promise<ToolExecutionResult> {
        // 实现工具执行
        // 这里需要更多的实现细节
        return {
            content: [{
                type: 'text',
                text: 'Tool execution not implemented yet'
            }]
        };
    }

    private buildUrl(template: string, args: any): string {
        // 实现URL构建
        return template;
    }

    private buildHeaders(tool: MCPTool): Record<string, string> {
        // 实现头部构建
        return {};
    }

    private processMultiParam(args: any, tool: MCPTool): any {
        // 实现多参数处理
        return args;
    }

    private buildBody(args: any, tool: MCPTool): any {
        // 实现请求体构建
        return args;
    }

    async run(): Promise<void> {
        const transport = new StdioServerTransport();
        await this.server.connect(transport);
        console.error('DingTalk MCP Server running on stdio');
    }
}
