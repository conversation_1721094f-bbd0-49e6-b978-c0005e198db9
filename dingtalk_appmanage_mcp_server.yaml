server:
  name: dingtalk-app-manage
  description: 钉钉应用管理
tools:
  # 应用管理类工具
  - name: createInnerApp
    description: 创建企业内部应用，支持H5微应用和小程序两种类型
    requestTemplate:
      method: POST
      url: https://api.dingtalk.com/v1.0/microApp/apps
    args:
      - name: opUnionId
        description: 操作人的unionId，该用户必须是拥有应用管理权限的管理员
        type: string
        required: true
        position: body
      - name: name
        description: 应用名称，长度范围要求2-20个字符
        type: string
        required: true
        position: body
      - name: desc
        description: 应用描述，最大长度200个字符
        type: string
        required: true
        position: body
      - name: icon
        description: 应用图标media_id
        type: string
        required: false
        position: body
      - name: homepageLink
        description: 应用首页地址，H5微应用时必传
        type: string
        required: false
        position: body
      - name: pcHomepageLink
        description: 应用PC端地址
        type: string
        required: false
        position: body
      - name: ompLink
        description: 应用管理后台地址
        type: string
        required: false
        position: body
      - name: ipWhiteList
        description: 服务器出口IP白名单列表，最大值50
        type: array
        required: false
        position: body
        items:
          type: string
      - name: scopeType
        description: 权限类型，目前只支持BASE
        type: string
        required: false
        position: body
      - name: developType
        description: 创建的内部应用类型，0为H5微应用，1为小程序，默认为0
        type: number
        required: false
        position: body

  - name: updateInnerApp
    description: 更新企业内部应用信息，包括应用名称、描述、图标、链接等
    requestTemplate:
      method: PUT
      url: https://api.dingtalk.com/v1.0/microApp/apps/{agentId}
    args:
      - name: agentId
        description: 应用的agentId
        type: number
        required: true
        position: path
      - name: opUnionId
        description: 操作更新的员工unionId，必须有该应用的管理权限
        type: string
        required: true
        position: body
      - name: name
        description: 应用名称，长度范围要求2-20个字符
        type: string
        required: false
        position: body
      - name: desc
        description: 应用描述，最大长度200个字符
        type: string
        required: false
        position: body
      - name: icon
        description: 应用图标media_id
        type: string
        required: false
        position: body
      - name: homepageLink
        description: 应用首页地址
        type: string
        required: false
        position: body
      - name: pcHomepageLink
        description: 应用PC端地址
        type: string
        required: false
        position: body
      - name: ompLink
        description: 应用管理后台地址
        type: string
        required: false
        position: body
      - name: ipWhiteList
        description: 服务器出口ip白名单
        type: array
        required: false
        position: body
        items:
          type: string

  - name: listAllApp
    description: 获取企业所有应用列表，包括基础应用、自建应用和第三方应用
    requestTemplate:
      method: GET
      url: https://api.dingtalk.com/v1.0/microApp/allApps
    args: []

  - name: listAllInnerApps
    description: 获取企业内部所有应用列表，仅限内部开发的应用
    requestTemplate:
      method: GET
      url: https://api.dingtalk.com/v1.0/microApp/allInnerApps
    args: []

  - name: listUserVilebleApp
    description: 获取指定用户可使用的企业应用列表及应用信息
    requestTemplate:
      method: GET
      url: https://api.dingtalk.com/v1.0/microApp/users/{userId}/apps
    args:
      - name: userId
        description: 用户的userId值
        type: string
        required: true
        position: path

  # 权限范围管理类工具
  - name: getMicroAppScope
    description: 获取企业内部应用的可使用范围信息（用户、部门、角色）
    requestTemplate:
      method: GET
      url: https://api.dingtalk.com/v1.0/microApp/apps/{agentId}/scopes
    args:
      - name: agentId
        description: 应用agentId
        type: number
        required: true
        position: path

  - name: setMicroAppScope
    description: 更新企业内部应用的可使用范围，支持增删用户、部门、角色
    requestTemplate:
      method: POST
      url: https://api.dingtalk.com/v1.0/microApp/apps/{agentId}/scopes
    args:
      - name: agentId
        description: 应用agentId
        type: number
        required: true
        position: path
      - name: addUserIds
        description: 增加的可使用用户userId列表，最大长度100
        type: array
        required: false
        position: body
        items:
          type: string
      - name: delUserIds
        description: 删除的可使用用户userId列表，最大长度100
        type: array
        required: false
        position: body
        items:
          type: string
      - name: addDeptIds
        description: 增加的可使用部门ID列表，最大长度100
        type: array
        required: false
        position: body
        items:
          type: number
      - name: delDeptIds
        description: 删除的可使用部门ID列表，最大长度100
        type: array
        required: false
        position: body
        items:
          type: number
      - name: addRoleIds
        description: 增加的可使用角色列表，最大长度100
        type: array
        required: false
        position: body
        items:
          type: number
      - name: delRoleIds
        description: 删除的可使用角色列表，最大长度100
        type: array
        required: false
        position: body
        items:
          type: number
      - name: onlyAdminVisible
        description: 是否仅管理员可使用，true为是，false为否
        type: boolean
        required: false
        position: body

  # 小程序版本管理类工具
  - name: publishInnerAppVersion
    description: 发布企业内部小程序版本，支持线上版本和体验版本
    requestTemplate:
      method: POST
      url: https://api.dingtalk.com/v1.0/microApp/innerMiniApps/{agentId}/versions/publish
    args:
      - name: agentId
        description: 应用AgentId
        type: number
        required: true
        position: path
      - name: appVersionId
        description: 小程序版本id，用于唯一标识小程序版本信息
        type: number
        required: true
        position: body
      - name: opUnionId
        description: 操作人的unionId，该用户必须是拥有应用管理权限的管理员
        type: string
        required: true
        position: body
      - name: publishType
        description: 小程序发布类型，online为发布线上版本，experience为发布体验版本
        type: string
        required: false
        position: body
      - name: miniAppOnPc
        description: 是否支持PC端打开小程序，false为只发布移动端，true为既发布移动端又发布PC端
        type: boolean
        required: false
        position: body

  - name: rollbackInnerAppVersion
    description: 回滚企业内部小程序到历史版本
    requestTemplate:
      method: POST
      url: https://api.dingtalk.com/v1.0/microApp/innerMiniApps/{agentId}/versions/rollback
    args:
      - name: agentId
        description: 应用AgentId
        type: number
        required: true
        position: path
      - name: versionId
        description: 小程序版本id
        type: number
        required: true
        position: body
      - name: opUnionId
        description: 操作人的unionId，该用户必须是拥有应用管理权限的管理员
        type: string
        required: true
        position: body

  - name: listInnerAppVersion
    description: 获取企业内部小程序的所有版本信息
    requestTemplate:
      method: GET
      url: https://api.dingtalk.com/v1.0/microApp/innerMiniApps/{agentId}/versions
    args:
      - name: agentId
        description: 应用AgentId
        type: number
        required: true
        position: path

  - name: pageInnerAppHistoryVersion
    description: 分页获取企业内部小程序历史版本列表
    requestTemplate:
      method: GET
      url: https://api.dingtalk.com/v1.0/microApp/innerMiniApps/{agentId}/historyVersions
    args:
      - name: agentId
        description: 应用AgentId
        type: number
        required: true
        position: path
      - name: pageNumber
        description: 当前页
        type: number
        required: true
        position: query
      - name: pageSize
        description: 本次读取的最大数据记录数量
        type: number
        required: true
        position: query