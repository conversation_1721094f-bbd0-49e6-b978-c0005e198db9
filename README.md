# 钉钉MCP Server

一个基于 TypeScript 的钉钉 MCP 服务器，提供钉钉各种功能的集成。

## 🚀 功能特性

- 钉钉通讯录
- 钉钉部门管理
- 钉钉机器人发消息/DING
- 钉钉企业文化荣誉
- 钉钉待办
- 钉钉日程
- 钉钉签到
- 钉钉工作通知
- 钉钉应用管理
- 钉钉服务窗
- 钉钉项目管理
- 钉钉日志

## 开发环境设置

### 安装依赖

```bash
npm install
```

### 构建项目

```bash
npm run build
```

### 开发模式

```bash
npm run dev
```

### 启动服务

```bash
npm start
```

## 环境变量配置

需要设置以下环境变量：

```bash
DINGTALK_Client_ID=your_dingtalk_client_id
DINGTALK_Client_Secret=your_dingtalk_client_secret
ACTIVE_PROFILES=dingtalk-contacts,dingtalk-calendar
```

### 可用的 Profile

| ProfileId                   | Description        | Permission                                       |
|-----------------------------|--------------------|--------------------------------------------------|
| dingtalk-contacts           | 钉钉通讯录，默认激活         | qyapi_addresslist_search   qyapi_get_member	                      
| dingtalk-department         | 钉钉部门管理             |qyapi_get_department_list	qyapi_get_department_member	
| dingtalk-robot-send-message | 钉钉机器人发消息/DING，默认激活 | 需要企业内机器人发送消息权限 <br/>Premium.Ding.Write	                                  |
| dingtalk-honor              | 钉钉企业文化荣誉           |OrgCulture.Honor.Read	OrgCulture.Honor.Read	
| dingtalk-tasks              | 钉钉待办               | Todo.Todo.Write<br>Todo.Todo.Read                |
| dingtalk-calendar           | 钉钉日程               |Calendar.Event.Write	Calendar.Event.Read	 Calendar.EventSchedule.Read	
| dingtalk-checkin            | 钉钉签到               |qyapi_checkin_read
| dingtalk-notice             | 钉钉工作通知             |
| dingtalk-app-manage         | 钉钉应用管理             | qyapi_microapp_manage<br>qyapi_get_microapp_list |
| dingtalk-service-window     | 钉钉服务窗              |    OfficialAccount.Message.Send	OfficialAccount.Contact.Read	OfficialAccount.Account.Read	                                              |
| dingtalk-teambition         | 钉钉项目管理             |  Project.Project.Write.All	Project.Project.Read.All	Project.Task.Write.All	Project.Task.Read.All	                                                |
| dingtalk-report             | 钉钉日志               | qyapi_report_statistics qyapi_report_manage	qyapi_report_query	                        |

## 项目结构

```
src/
├── DingTalkMCPServer.ts    # 主服务器类
├── cli.ts                  # 命令行入口
├── index.ts               # 模块导出
├── types.ts               # 类型定义
└── utils/
    ├── messageBuilder.ts   # 消息构建工具
    └── logBuilder.ts      # 日志构建工具
```

## 许可证

MIT
